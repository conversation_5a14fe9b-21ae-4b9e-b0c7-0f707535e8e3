"use client";

import React, { useRef, useEffect, useState, useCallback } from "react";
import { ZoomControls } from "./ZoomControls";
import { createMagicWandCursor } from "./MagicCursor";
import { Button } from "@/components/ui/button";
import { removeBackground, canvasToBase64, getDefaultBackgrounds } from "@/lib/background-removal";
import {
  Undo,
  Redo,
  Trash2,
  Wand2,
  Sparkles,
  Eye,
  UnfoldHorizontal,
  Image as ImageIcon,
  ChevronDown,
} from "lucide-react";
import type { ImageData } from "./ImageEditor";

export interface MaskState {
  dataURL: string;
  width: number;
  height: number;
}

interface CanvasEditorProps {
  imageData: ImageData;
  onProcessImage: (maskCanvas: HTMLCanvasElement, currentCanvas: HTMLCanvasElement) => void;
  disabled?: boolean;
  brushSettings?: BrushSettings;
  onBrushSettingsChange?: (settings: BrushSettings) => void;
  // Mask state management
  initialMaskState?: MaskState; // Mask state with size info
  onMaskStateChange?: (maskState: MaskState) => void;
  // History state management
  initialHistoryState?: { history: string[]; historyIndex: number };
  onHistoryStateChange?: (history: string[], historyIndex: number) => void;
  // Processing state
  isProcessing?: boolean;
  // Help callback
  onShowHelp?: () => void;
  // Comparison feature
  processedImageUrl?: string | null;
  // Image update callback for background removal
  onImageUpdate?: (newImageUrl: string, imageId?: string) => void;
  // Background removal callbacks
  onBackgroundRemoval?: () => Promise<{ resultBase64: string; imageId: string }>;
  onBackgroundReplacement?: (backgroundUrl: string) => Promise<void>;
  // Current image ID for state isolation
  currentImageId?: string;
  // Original image URL (before any processing)
  originalImageUrl?: string;
}

interface BrushSettings {
  size: number;
  opacity: number;
  color: string;
  shape: import("./MagicCursor").CursorShape;
}

export const CanvasEditor: React.FC<CanvasEditorProps> = ({
  imageData,
  onProcessImage,
  disabled = false,
  brushSettings: externalBrushSettings,
  onBrushSettingsChange,
  initialMaskState,
  onMaskStateChange,
  initialHistoryState,
  onHistoryStateChange,
  isProcessing = false,
  onShowHelp,
  processedImageUrl,
  onImageUpdate,
  onBackgroundRemoval,
  onBackgroundReplacement,
  currentImageId,
  originalImageUrl,
}) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const maskCanvasRef = useRef<HTMLCanvasElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  const [isDrawing, setIsDrawing] = useState(false);
  const [internalBrushSettings, setInternalBrushSettings] =
    useState<BrushSettings>({
      size: 20,
      opacity: 100,
      color: "#ff3333",
      shape: "magic-wand",
    });
  const [showComparison, setShowComparison] = useState(false);
  const [comparisonProgress, setComparisonProgress] = useState(0);
  const [comparisonStartTime, setComparisonStartTime] = useState<number | null>(
    null
  );
  const [showBackgroundOptions, setShowBackgroundOptions] = useState(false);
  const [removedBackgroundImage, setRemovedBackgroundImage] = useState<string | null>(null);
  const [currentCanvasSnapshot, setCurrentCanvasSnapshot] = useState<string | null>(null);

  // Use external brush settings if provided, otherwise use internal
  const brushSettings = externalBrushSettings || internalBrushSettings;

  // Helper function to create mask state
  const createMaskState = useCallback(
    (canvas: HTMLCanvasElement): MaskState => {
      return {
        dataURL: canvas.toDataURL(),
        width: canvas.width,
        height: canvas.height,
      };
    },
    []
  );

  // Helper function to draw a line between two points
  const drawLine = useCallback(
    (
      ctx: CanvasRenderingContext2D,
      from: { x: number; y: number },
      to: { x: number; y: number }
    ) => {
      const distance = Math.sqrt(
        Math.pow(to.x - from.x, 2) + Math.pow(to.y - from.y, 2)
      );
      const steps = Math.max(
        1,
        Math.floor(distance / (brushSettings.size / 4))
      ); // More steps for smoother lines

      for (let i = 0; i <= steps; i++) {
        const t = i / steps;
        const x = from.x + (to.x - from.x) * t;
        const y = from.y + (to.y - from.y) * t;

        ctx.beginPath();
        ctx.arc(x, y, brushSettings.size / 2, 0, Math.PI * 2);
        ctx.fill();
      }
    },
    [brushSettings.size]
  );

  const [zoom, setZoom] = useState(1);
  const [canvasSize, setCanvasSize] = useState({ width: 0, height: 0 });
  const [pan, setPan] = useState({ x: 0, y: 0 });
  const [isPanning, setIsPanning] = useState(false);
  const [lastPanPoint, setLastPanPoint] = useState({ x: 0, y: 0 });
  const [lastDrawPoint, setLastDrawPoint] = useState<{
    x: number;
    y: number;
  } | null>(null);
  const [previousDrawPoint, setPreviousDrawPoint] = useState<{
    x: number;
    y: number;
  } | null>(null);
  const [history, setHistory] = useState<string[]>(
    initialHistoryState?.history || []
  );
  const [historyIndex, setHistoryIndex] = useState(
    initialHistoryState?.historyIndex || -1
  );

  // Sync history state when props change (image switching)
  useEffect(() => {
    if (initialHistoryState) {
      setHistory(initialHistoryState.history);
      setHistoryIndex(initialHistoryState.historyIndex);
    } else {
      // Reset to empty state for new images
      setHistory([]);
      setHistoryIndex(-1);
    }
  }, [initialHistoryState]);

  // Restore mask state when switching images
  useEffect(() => {
    const canvas = maskCanvasRef.current;
    if (!canvas || !canvasSize.width || !canvasSize.height) return;

    const ctx = canvas.getContext("2d");
    if (!ctx) return;

    // Clear current mask
    ctx.clearRect(0, 0, canvas.width, canvas.height);

    // Restore mask state if available
    if (initialMaskState) {
      const img = new Image();
      img.onload = () => {
        // Scale the mask to fit current canvas size
        const scaleX = canvasSize.width / initialMaskState.width;
        const scaleY = canvasSize.height / initialMaskState.height;

        // If the aspect ratios match, scale uniformly
        if (Math.abs(scaleX - scaleY) < 0.01) {
          ctx.drawImage(img, 0, 0, canvasSize.width, canvasSize.height);
        } else {
          // If aspect ratios don't match, this might be from a different image
          // Only restore if dimensions match exactly to avoid confusion
          if (
            initialMaskState.width === canvasSize.width &&
            initialMaskState.height === canvasSize.height
          ) {
            ctx.drawImage(img, 0, 0, canvasSize.width, canvasSize.height);
          }
        }
      };
      img.src = initialMaskState.dataURL;
    }
  }, [initialMaskState, canvasSize]);

  // Save initial blank state for new images (when no history exists)
  useEffect(() => {
    const canvas = maskCanvasRef.current;
    if (!canvas || !canvasSize.width || !canvasSize.height) return;

    // Only save initial state if no history exists (new image)
    if (history.length === 0 && historyIndex === -1) {
      const dataURL = canvas.toDataURL();
      setHistory([dataURL]);
      setHistoryIndex(0);

      // Notify parent about initial state
      if (onHistoryStateChange) {
        onHistoryStateChange([dataURL], 0);
      }
      if (onMaskStateChange) {
        onMaskStateChange(createMaskState(canvas));
      }
    }
  }, [
    canvasSize,
    history.length,
    historyIndex,
    onHistoryStateChange,
    onMaskStateChange,
    createMaskState,
  ]);

  // Maximum canvas dimensions
  const MAX_CANVAS_WIDTH = 800;
  const MAX_CANVAS_HEIGHT = 600;

  // Initialize canvas
  useEffect(() => {
    if (!imageData || !canvasRef.current || !maskCanvasRef.current) return;

    // Only initialize if this is for the current image
    if (currentImageId && imageData.id !== currentImageId) {
      return;
    }

    const canvas = canvasRef.current;
    const maskCanvas = maskCanvasRef.current;
    const ctx = canvas.getContext("2d");
    const maskCtx = maskCanvas.getContext("2d");

    if (!ctx || !maskCtx) return;

    // Calculate canvas size with maximum constraints
    const aspectRatio = imageData.width / imageData.height;
    let newWidth = Math.min(imageData.width, MAX_CANVAS_WIDTH);
    let newHeight = newWidth / aspectRatio;

    if (newHeight > MAX_CANVAS_HEIGHT) {
      newHeight = MAX_CANVAS_HEIGHT;
      newWidth = newHeight * aspectRatio;
    }

    setCanvasSize({ width: newWidth, height: newHeight });

    // Set canvas dimensions
    canvas.width = newWidth;
    canvas.height = newHeight;
    maskCanvas.width = newWidth;
    maskCanvas.height = newHeight;

    // Draw image
    const img = new Image();
    img.onload = () => {
      // Double check we're still on the same image before drawing
      if (currentImageId && imageData.id === currentImageId) {
        ctx.drawImage(img, 0, 0, newWidth, newHeight);
      }
    };
    img.src = imageData.url;

    // Clear mask (restoration is handled by separate useEffect)
    maskCtx.clearRect(0, 0, newWidth, newHeight);
  }, [imageData, currentImageId]);

  // Update canvas when processed result is available (for background removal)
  useEffect(() => {
    if (!processedImageUrl || !canvasRef.current) return;

    // Only update if this is for the current image
    if (currentImageId && imageData.id !== currentImageId) {
      return;
    }

    const canvas = canvasRef.current;
    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    const img = new Image();
    img.onload = () => {
      // Double check we're still on the same image before drawing
      if (currentImageId && imageData.id === currentImageId) {
        // Update canvas size to match the processed image
        const aspectRatio = img.width / img.height;
        let newWidth = Math.min(img.width, MAX_CANVAS_WIDTH);
        let newHeight = newWidth / aspectRatio;

        if (newHeight > MAX_CANVAS_HEIGHT) {
          newHeight = MAX_CANVAS_HEIGHT;
          newWidth = newHeight * aspectRatio;
        }

        canvas.width = newWidth;
        canvas.height = newHeight;
        setCanvasSize({ width: newWidth, height: newHeight });

        // Clear and draw processed image
        ctx.clearRect(0, 0, canvas.width, canvas.height);
        ctx.drawImage(img, 0, 0, newWidth, newHeight);

        // Save the processed image for future background replacements
        setRemovedBackgroundImage(canvas.toDataURL());
      }
    };
    img.src = processedImageUrl;
  }, [processedImageUrl, currentImageId, imageData.id]);

  const startDrawing = useCallback(
    (e: React.MouseEvent | React.TouchEvent) => {
      if (disabled || isPanning || isProcessing) return;
      setIsDrawing(true);

      const canvas = maskCanvasRef.current;
      if (!canvas) return;

      const rect = canvas.getBoundingClientRect();
      const clientX = "touches" in e ? e.touches[0].clientX : e.clientX;
      const clientY = "touches" in e ? e.touches[0].clientY : e.clientY;

      // Account for zoom and pan transformations
      const x =
        (clientX - rect.left - rect.width / 2) / zoom + canvasSize.width / 2;
      const y =
        (clientY - rect.top - rect.height / 2) / zoom + canvasSize.height / 2;

      // Store the starting point but don't draw yet
      setLastDrawPoint({ x, y });
      setPreviousDrawPoint({ x, y });
    },
    [disabled, isPanning, isProcessing, zoom, canvasSize]
  );

  const draw = useCallback(
    (e: React.MouseEvent | React.TouchEvent) => {
      if (!isDrawing || disabled || isPanning || isProcessing) return;

      const canvas = maskCanvasRef.current;
      if (!canvas) return;

      const rect = canvas.getBoundingClientRect();
      const clientX = "touches" in e ? e.touches[0].clientX : e.clientX;
      const clientY = "touches" in e ? e.touches[0].clientY : e.clientY;

      // Account for zoom and pan transformations
      const x =
        (clientX - rect.left - rect.width / 2) / zoom + canvasSize.width / 2;
      const y =
        (clientY - rect.top - rect.height / 2) / zoom + canvasSize.height / 2;

      const ctx = canvas.getContext("2d");
      if (!ctx) return;

      ctx.globalCompositeOperation = "source-over";
      ctx.fillStyle = brushSettings.color;
      ctx.globalAlpha = brushSettings.opacity / 100;

      // If this is the first draw after starting, draw the starting point too
      if (lastDrawPoint) {
        ctx.beginPath();
        ctx.arc(
          lastDrawPoint.x,
          lastDrawPoint.y,
          brushSettings.size / 2,
          0,
          Math.PI * 2
        );
        ctx.fill();
        setLastDrawPoint(null); // Clear the starting point
      }

      // Draw line from previous point to current point for smooth drawing
      if (previousDrawPoint) {
        drawLine(ctx, previousDrawPoint, { x, y });
      } else {
        // Draw current point if no previous point
        ctx.beginPath();
        ctx.arc(x, y, brushSettings.size / 2, 0, Math.PI * 2);
        ctx.fill();
      }

      // Update previous point
      setPreviousDrawPoint({ x, y });
    },
    [
      isDrawing,
      disabled,
      isPanning,
      isProcessing,
      zoom,
      brushSettings,
      canvasSize,
      lastDrawPoint,
      previousDrawPoint,
      drawLine,
    ]
  );

  // Save current state to history
  const saveToHistory = useCallback(() => {
    const canvas = maskCanvasRef.current;
    if (!canvas) return;

    const dataURL = canvas.toDataURL();
    const newIndex = Math.min(historyIndex + 1, 19);

    setHistory((prev) => {
      const newHistory = prev.slice(0, historyIndex + 1);
      newHistory.push(dataURL);
      const finalHistory = newHistory.slice(-20); // Keep only last 20 states

      // Notify parent component about history change
      if (onHistoryStateChange) {
        onHistoryStateChange(finalHistory, newIndex);
      }

      return finalHistory;
    });

    setHistoryIndex(newIndex);

    // Also notify about mask state change
    if (onMaskStateChange) {
      onMaskStateChange(createMaskState(canvas));
    }
  }, [historyIndex, onHistoryStateChange, onMaskStateChange, createMaskState]);

  // Save both main canvas and mask state to history (for background operations)
  const saveMainCanvasToHistory = useCallback(() => {
    const canvas = canvasRef.current;
    const maskCanvas = maskCanvasRef.current;
    if (!canvas || !maskCanvas) return;

    // Save the current main canvas state by updating the image data
    const mainCanvasDataURL = canvas.toDataURL();
    onImageUpdate?.(mainCanvasDataURL);

    // Also save the current mask state to history
    const maskDataURL = maskCanvas.toDataURL();
    const newIndex = Math.min(historyIndex + 1, 19);

    setHistory((prev) => {
      const newHistory = prev.slice(0, historyIndex + 1);
      newHistory.push(maskDataURL);
      const finalHistory = newHistory.slice(-20); // Keep only last 20 states

      // Notify parent component about history change
      if (onHistoryStateChange) {
        onHistoryStateChange(finalHistory, newIndex);
      }

      return finalHistory;
    });

    setHistoryIndex(newIndex);
  }, [historyIndex, onHistoryStateChange, onImageUpdate]);

  const stopDrawing = useCallback(() => {
    if (isDrawing) {
      setIsDrawing(false);
      setPreviousDrawPoint(null); // Reset previous point
      // Save to history after drawing
      setTimeout(() => saveToHistory(), 0);
    }
  }, [isDrawing, saveToHistory]);

  const clearMask = useCallback(() => {
    const canvas = maskCanvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext("2d");
    if (!ctx) return;

    ctx.clearRect(0, 0, canvas.width, canvas.height);
    // Save to history after clearing
    setTimeout(() => saveToHistory(), 0);
  }, [saveToHistory]);

  const handleProcess = useCallback(() => {
    if (!maskCanvasRef.current || !canvasRef.current) return;
    onProcessImage(maskCanvasRef.current, canvasRef.current);
  }, [onProcessImage]);

  // Comparison handlers
  const handleCompareStart = () => {
    if (processedImageUrl) {
      // Capture current canvas state for comparison
      const canvas = canvasRef.current;
      if (canvas) {
        const currentSnapshot = canvas.toDataURL();
        setCurrentCanvasSnapshot(currentSnapshot);
      }

      setShowComparison(true);
      setComparisonProgress(0);
      setComparisonStartTime(Date.now());
    }
  };

  const handleCompareEnd = () => {
    setShowComparison(false);
    setComparisonProgress(0);
    setComparisonStartTime(null);
  };

  // Background removal handlers
  const handleBackgroundRemoval = async () => {
    if (disabled || isProcessing || !onBackgroundRemoval) return;

    // Capture the current image ID at the start of the operation
    const operationImageId = currentImageId;
    if (!operationImageId) return;

    try {
      // Save current state to history before processing
      saveMainCanvasToHistory();

      // Call the background removal callback
      const { resultBase64, imageId } = await onBackgroundRemoval();

      // Only update canvas if this result is for the image we started with
      // AND we're still viewing the same image
      if (imageId === operationImageId && imageId === currentImageId) {
        const canvas = canvasRef.current;
        const maskCanvas = maskCanvasRef.current;
        if (canvas && maskCanvas) {
          const img = new Image();
          img.onload = () => {
            // Triple check: operation image, result image, and current image must all match
            if (imageId === operationImageId && imageId === currentImageId) {
              const ctx = canvas.getContext('2d');
              const maskCtx = maskCanvas.getContext('2d');
              if (ctx && maskCtx) {
                // Calculate display size while maintaining aspect ratio
                const aspectRatio = img.width / img.height;
                const MAX_CANVAS_WIDTH = 800;
                const MAX_CANVAS_HEIGHT = 600;

                let newWidth = img.width;
                let newHeight = img.height;

                // Scale down if too large
                if (newWidth > MAX_CANVAS_WIDTH) {
                  newWidth = MAX_CANVAS_WIDTH;
                  newHeight = newWidth / aspectRatio;
                }
                if (newHeight > MAX_CANVAS_HEIGHT) {
                  newHeight = MAX_CANVAS_HEIGHT;
                  newWidth = newHeight * aspectRatio;
                }

                // Update canvas size to match the new image
                canvas.width = newWidth;
                canvas.height = newHeight;
                maskCanvas.width = newWidth;
                maskCanvas.height = newHeight;

                // Update canvas size state for display
                setCanvasSize({ width: newWidth, height: newHeight });

                // Clear and draw new image
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                ctx.drawImage(img, 0, 0, newWidth, newHeight);

                // Clear mask canvas as well
                maskCtx.clearRect(0, 0, maskCanvas.width, maskCanvas.height);

                // Save the removed background image for future background replacements
                setRemovedBackgroundImage(canvas.toDataURL());

                // Note: We don't call onImageUpdate here to avoid triggering canvas reinitialization
                // The result is already stored in processedResults by the ImageEditor
              }
            }
          };
          img.onerror = () => {
            console.error('Failed to load processed image');
          };
          img.src = `data:image/png;base64,${resultBase64}`;
        }
      }
    } catch (error) {
      console.error('Background removal failed:', error);
    }
  };

  // Background replacement handler (local processing)
  const handleBackgroundReplacement = async (backgroundUrl: string) => {
    if (disabled || isProcessing || !onBackgroundReplacement) return;

    setShowBackgroundOptions(false);

    try {
      // Save current state to history before processing
      saveMainCanvasToHistory();

      // Call the background replacement callback
      await onBackgroundReplacement(backgroundUrl);

    } catch (error) {
      console.error('Background replacement failed:', error);
    }
  };

  // Animation effect for comparison
  useEffect(() => {
    if (!showComparison || !comparisonStartTime) return;

    const animateProgress = () => {
      const elapsed = Date.now() - comparisonStartTime;
      const progress = Math.min(elapsed / 400, 1); // 1 second for full sweep
      setComparisonProgress(progress);

      if (progress < 1 && showComparison) {
        requestAnimationFrame(animateProgress);
      }
    };

    const animationId = requestAnimationFrame(animateProgress);

    return () => {
      cancelAnimationFrame(animationId);
    };
  }, [showComparison, comparisonStartTime]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      setShowComparison(false);
      setComparisonProgress(0);
      setComparisonStartTime(null);
    };
  }, []);

  // Close background options when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (showBackgroundOptions) {
        const target = event.target as Element;
        if (!target.closest('.relative')) {
          setShowBackgroundOptions(false);
        }
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [showBackgroundOptions]);

  // Handle mouse wheel zoom
  const handleWheel = useCallback(
    (e: React.WheelEvent) => {
      e.preventDefault();
      const delta = e.deltaY > 0 ? -0.1 : 0.1;
      const newZoom = Math.max(0.1, Math.min(3, zoom + delta));
      setZoom(newZoom);
    },
    [zoom]
  );

  // Handle pan start
  const handlePanStart = useCallback((e: React.MouseEvent) => {
    if (e.button === 1 || (e.button === 0 && e.altKey)) {
      // Middle mouse or Alt+Left mouse
      e.preventDefault();
      setIsPanning(true);
      setLastPanPoint({ x: e.clientX, y: e.clientY });
    }
  }, []);

  // Handle pan move
  const handlePanMove = useCallback(
    (e: React.MouseEvent) => {
      if (isPanning) {
        e.preventDefault();
        const deltaX = e.clientX - lastPanPoint.x;
        const deltaY = e.clientY - lastPanPoint.y;

        setPan((prev) => ({
          x: prev.x + deltaX,
          y: prev.y + deltaY,
        }));

        setLastPanPoint({ x: e.clientX, y: e.clientY });
      }
    },
    [isPanning, lastPanPoint]
  );

  // Handle pan end
  const handlePanEnd = useCallback(() => {
    setIsPanning(false);
  }, []);

  // Reset zoom and pan
  const resetView = useCallback(() => {
    setZoom(1);
    setPan({ x: 0, y: 0 });
  }, []);

  // Undo function
  const undo = useCallback(() => {
    if (historyIndex <= 0) return;

    const canvas = maskCanvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext("2d");
    if (!ctx) return;

    const newIndex = historyIndex - 1;
    const imageData = history[newIndex];

    if (imageData) {
      const img = new Image();
      img.onload = () => {
        ctx.clearRect(0, 0, canvas.width, canvas.height);
        ctx.drawImage(img, 0, 0);

        // Notify parent about state changes
        if (onMaskStateChange) {
          onMaskStateChange(createMaskState(canvas));
        }
      };
      img.src = imageData;
    } else {
      ctx.clearRect(0, 0, canvas.width, canvas.height);
      if (onMaskStateChange) {
        onMaskStateChange(createMaskState(canvas));
      }
    }

    setHistoryIndex(newIndex);

    // Notify parent about history change
    if (onHistoryStateChange) {
      onHistoryStateChange(history, newIndex);
    }
  }, [
    historyIndex,
    history,
    onMaskStateChange,
    onHistoryStateChange,
    createMaskState,
  ]);

  // Redo function
  const redo = useCallback(() => {
    if (historyIndex >= history.length - 1) return;

    const canvas = maskCanvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext("2d");
    if (!ctx) return;

    const newIndex = historyIndex + 1;
    const imageData = history[newIndex];

    const img = new Image();
    img.onload = () => {
      ctx.clearRect(0, 0, canvas.width, canvas.height);
      ctx.drawImage(img, 0, 0);

      // Notify parent about state changes
      if (onMaskStateChange) {
        onMaskStateChange(createMaskState(canvas));
      }
    };
    img.src = imageData;

    setHistoryIndex(newIndex);

    // Notify parent about history change
    if (onHistoryStateChange) {
      onHistoryStateChange(history, newIndex);
    }
  }, [
    historyIndex,
    history,
    onMaskStateChange,
    onHistoryStateChange,
    createMaskState,
  ]);

  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      const isMac = /Mac|iPod|iPhone|iPad/.test(navigator.platform);
      const ctrlKey = isMac ? e.metaKey : e.ctrlKey;

      if (ctrlKey && e.key === "z" && !e.shiftKey) {
        e.preventDefault();
        undo();
      } else if (ctrlKey && (e.key === "y" || (e.key === "z" && e.shiftKey))) {
        e.preventDefault();
        redo();
      }
    };

    document.addEventListener("keydown", handleKeyDown);
    return () => document.removeEventListener("keydown", handleKeyDown);
  }, [undo, redo]);

  return (
    <div className="h-full flex flex-col">
      {/* Top toolbar */}
      <div className="flex items-center justify-between p-3 bg-white border-b border-gray-200">
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={undo}
            disabled={disabled || isProcessing || historyIndex <= 0}
            className="border-gray-300 text-gray-700 hover:opacity-90"
            title="Undo (Ctrl+Z)"
          >
            <Undo className="w-4 h-4" />
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={redo}
            disabled={
              disabled || isProcessing || historyIndex >= history.length - 1
            }
            className="border-gray-300 text-gray-700 hover:opacity-90"
            title="Redo (Ctrl+Y)"
          >
            <Redo className="w-4 h-4" />
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={clearMask}
            disabled={disabled || isProcessing}
            className="border-gray-300 text-gray-700 hover:opacity-90"
            title="Clear All"
          >
            <Trash2 className="w-4 h-4" />
          </Button>
        </div>

        <div className="flex items-center gap-2">
          <ZoomControls
            zoom={zoom}
            onZoomChange={setZoom}
            onPanChange={setPan}
            disabled={disabled || isProcessing}
          />

          <div className="relative">
            <Button
              variant="outline"
              size="sm"
              onClick={handleBackgroundRemoval}
              disabled={disabled || isProcessing}
              className="border-gray-300 text-gray-700 hover:bg-gray-50"
              title="Remove background"
            >
              {isProcessing ? (
                <>
                  <Sparkles className="w-4 h-4 mr-2 animate-spin" />
                  Processing...
                </>
              ) : (
                <>
                  <ImageIcon className="w-4 h-4 mr-2" />
                  Remove BG
                </>
              )}
            </Button>

            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowBackgroundOptions(!showBackgroundOptions)}
              disabled={disabled || isProcessing}
              className="border-gray-300 text-gray-700 hover:bg-gray-50 ml-1"
              title="Replace background"
            >
              <ChevronDown className="w-4 h-4" />
            </Button>

            {/* Background options dropdown */}
            {showBackgroundOptions && (
              <div className="absolute top-full left-0 mt-1 bg-white border border-gray-200 rounded-lg shadow-lg z-50 min-w-[200px]">
                <div className="p-2">
                  <div className="text-xs font-medium text-gray-700 mb-2">Replace Background:</div>
                  {getDefaultBackgrounds().map((bg, index) => (
                    <button
                      key={index}
                      onClick={() => handleBackgroundReplacement(bg.url)}
                      className="w-full text-left px-2 py-1 text-sm hover:bg-gray-100 rounded flex items-center gap-2"
                    >
                      <div
                        className="w-4 h-4 rounded border border-gray-300"
                        style={{
                          backgroundImage: `url(${bg.thumbnail})`,
                          backgroundSize: 'cover',
                          backgroundPosition: 'center'
                        }}
                      />
                      {bg.name}
                    </button>
                  ))}
                </div>
              </div>
            )}
          </div>

          {processedImageUrl && (
            <Button
              variant="outline"
              size="sm"
              onMouseDown={handleCompareStart}
              onMouseUp={handleCompareEnd}
              onMouseLeave={handleCompareEnd}
              onTouchStart={handleCompareStart}
              onTouchEnd={handleCompareEnd}
              disabled={disabled || isProcessing}
              className="border-gray-300 text-gray-700 hover:bg-gray-500"
              title="Hold to compare with original"
            >
              <UnfoldHorizontal className="w-4 h-4 " />
              {/* Compare */}
            </Button>
          )}
          <Button
            onClick={handleProcess}
            disabled={disabled || isProcessing}
            className="bg-blue-600 hover:bg-blue-700 text-white shadow-sm"
          >
            <Wand2 className="w-4 h-4 mr-2" />
            {isProcessing ? "Processing..." : "Remove Objects"}
          </Button>
        </div>
      </div>

      {/* Canvas Container */}
      <div className="flex-1 relative overflow-hidden">
        <div
          ref={containerRef}
          className="absolute inset-0 canvas-container flex items-center justify-center"
          onWheel={handleWheel}
          onMouseDown={handlePanStart}
          onMouseMove={handlePanMove}
          onMouseUp={handlePanEnd}
          onMouseLeave={handlePanEnd}
          style={{ cursor: isPanning ? "grabbing" : "grab" }}
        >
          <div
            className="relative"
            style={{
              width: canvasSize.width,
              height: canvasSize.height,
              transform: `translate(${pan.x}px, ${pan.y}px) scale(${zoom})`,
              transformOrigin: "center center",
              transition: isPanning ? "none" : "transform 0.1s ease-out",
            }}
          >
            <canvas
              ref={canvasRef}
              className="absolute inset-0 rounded-lg shadow-lg"
              style={{
                width: canvasSize.width,
                height: canvasSize.height,
              }}
            />
            <canvas
              ref={maskCanvasRef}
              className="absolute inset-0 rounded-lg"
              style={{
                width: canvasSize.width,
                height: canvasSize.height,
                opacity: showComparison ? 0 : 0.6, // Hide mask during comparison
                cursor: isPanning
                  ? "grabbing"
                  : createMagicWandCursor(
                      brushSettings.shape,
                      Math.max(20, brushSettings.size)
                    ),
                pointerEvents: isPanning ? "none" : "auto",
                transition: "opacity 0.2s ease-in-out", // Smooth transition
              }}
              onMouseDown={startDrawing}
              onMouseMove={draw}
              onMouseUp={stopDrawing}
              onMouseLeave={stopDrawing}
              onTouchStart={startDrawing}
              onTouchMove={draw}
              onTouchEnd={stopDrawing}
            />

            {/* Processing Overlay */}
            {isProcessing && (
              <div className="absolute inset-0 bg-black/30 backdrop-blur-sm rounded-lg flex items-center justify-center z-50">
                <div className="bg-white/90 backdrop-blur-sm rounded-2xl p-8 shadow-2xl text-center">
                  <div className="relative mb-6">
                    <div className="w-16 h-16 mx-auto">
                      <Sparkles className="w-16 h-16 text-blue-500 animate-spin" />
                    </div>
                    <div className="absolute inset-0 flex items-center justify-center">
                      <div className="w-8 h-8 bg-blue-500 rounded-full animate-pulse"></div>
                    </div>
                  </div>
                  <h3 className="text-xl font-semibold text-gray-800 mb-2">
                    Processing Image
                  </h3>
                  <p className="text-gray-600 mb-4">
                    AI is removing unwanted objects...
                  </p>
                  <div className="flex items-center justify-center gap-2 text-sm text-gray-500">
                    <div className="w-2 h-2 bg-blue-500 rounded-full animate-bounce"></div>
                    <div className="w-2 h-2 bg-blue-500 rounded-full animate-bounce delay-100"></div>
                    <div className="w-2 h-2 bg-blue-500 rounded-full animate-bounce delay-200"></div>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Comparison Overlay - Shows original image with current canvas result sweeping from left to right */}
        {showComparison && currentCanvasSnapshot && (
          <div className="absolute inset-0 pointer-events-none">
            {/* Original image overlay (covers entire canvas when comparing) */}
            <div
              className="absolute inset-0 flex items-center justify-center"
              style={{
                backgroundColor: 'rgba(0, 0, 0, 0.1)', // Slight overlay to distinguish from normal view
              }}
            >
              <div
                className="relative"
                style={{
                  width: canvasSize.width,
                  height: canvasSize.height,
                  transform: `translate(${pan.x}px, ${pan.y}px) scale(${zoom})`,
                  transformOrigin: "center center",
                }}
              >
                {/* Original image as base (always visible during comparison) */}
                <div className="absolute inset-0">
                  <img
                    src={originalImageUrl || imageData.url}
                    alt="Original image"
                    className="w-full h-full object-contain rounded-lg"
                  />
                </div>

                {/* Processed result overlay (sweeps from left to right) */}
                <div
                  className="absolute inset-0"
                  style={{
                    clipPath: `inset(0 ${100 - comparisonProgress * 100}% 0 0)`,
                    transition:
                      comparisonProgress === 0
                        ? "none"
                        : "clip-path 0.1s ease-out",
                  }}
                >
                  <img
                    src={currentCanvasSnapshot}
                    alt="Current canvas result"
                    className="w-full h-full object-contain rounded-lg"
                  />
                </div>

                {/* Sweep line */}
                {comparisonProgress > 0 && comparisonProgress < 1 && (
                  <div
                    className="absolute top-0 bottom-0 w-0.5 bg-white shadow-lg z-10"
                    style={{
                      left: `${comparisonProgress * 100}%`,
                      transform: "translateX(-50%)",
                    }}
                  />
                )}
              </div>
            </div>
          </div>
        )}
      </div>


    </div>
  );
};
